import type { ExchangeInfo, TradingPair, TickerData, ExchangeVolumeData } from '@/types/exchange'

class ExchangeService {
  private exchanges: Map<string, any> = new Map()
  private exchangeInfoCache: ExchangeInfo[] = []
  private cacheExpiry: number = 0
  private readonly CACHE_DURATION = 5 * 60 * 1000 // 5 minutes
  private readonly SUPPORTED_EXCHANGES = [
    'binance', 'coinbase', 'kraken', 'bitfinex', 'huobi', 'okx', 'kucoin',
    'bybit', 'gate', 'mexc', 'bitget', 'gemini', 'bitstamp'
  ]
  private ccxt: any = null

  constructor() {
    this.initializeExchanges()
  }

  private async initializeExchanges() {
    try {
      console.log('Loading CCXT library...')
      // Dynamic import to avoid build issues
      this.ccxt = await import('ccxt')
      console.log('✓ CCXT library loaded successfully')

      console.log('Initializing CCXT exchanges...')

      for (const exchangeId of this.SUPPORTED_EXCHANGES) {
        try {
          // Check if exchange class exists in ccxt
          const ExchangeClass = this.ccxt[exchangeId]
          if (typeof ExchangeClass === 'function') {
            const exchange = new ExchangeClass({
              timeout: 30000,
              enableRateLimit: true,
              sandbox: false,
              // Use CORS proxy for browser requests
              proxy: '',
              headers: {
                'User-Agent': 'ccxt/browser'
              }
            })

            // Only add exchanges that support ticker fetching
            if (exchange.has.fetchTickers || exchange.has.fetchTicker) {
              this.exchanges.set(exchangeId, exchange)
              console.log(`✓ Initialized ${exchangeId}`)
            }
          }
        } catch (error) {
          console.warn(`✗ Failed to initialize ${exchangeId}:`, error)
        }
      }

      console.log(`Successfully initialized ${this.exchanges.size} exchanges`)
    } catch (error) {
      console.error('Failed to load CCXT library:', error)
      // Fallback to mock data if CCXT fails to load
      this.initializeFallbackData()
    }
  }

  private initializeFallbackData() {
    console.log('Using fallback mock data...')
    // Add mock exchanges for demonstration
    for (const exchangeId of this.SUPPORTED_EXCHANGES.slice(0, 5)) {
      this.exchanges.set(exchangeId, {
        id: exchangeId,
        name: this.getExchangeName(exchangeId),
        has: { fetchTickers: true, fetchTicker: true },
        rateLimit: 1000
      })
    }
  }

  async getAllExchanges(): Promise<ExchangeInfo[]> {
    if (this.exchangeInfoCache.length > 0 && Date.now() < this.cacheExpiry) {
      return this.exchangeInfoCache
    }

    const exchangeInfos: ExchangeInfo[] = []

    for (const [id, exchange] of this.exchanges) {
      try {
        const info: ExchangeInfo = {
          id: exchange.id,
          name: exchange.name || this.getExchangeName(id),
          countries: exchange.countries || [],
          urls: {
            logo: exchange.urls?.logo || '',
            www: exchange.urls?.www || '',
            api: exchange.urls?.api || ''
          },
          has: {
            fetchTicker: exchange.has.fetchTicker || false,
            fetchTickers: exchange.has.fetchTickers || false,
            fetchOrderBook: exchange.has.fetchOrderBook || false,
            fetchTrades: exchange.has.fetchTrades || false,
            fetchOHLCV: exchange.has.fetchOHLCV || false
          },
          rateLimit: exchange.rateLimit || 1000,
          certified: exchange.certified || false,
          pro: exchange.pro || false,
          sandbox: exchange.sandbox || false,
          status: 'ok'
        }
        exchangeInfos.push(info)
      } catch (error) {
        console.warn(`Failed to get info for exchange ${id}:`, error)
      }
    }

    this.exchangeInfoCache = exchangeInfos
    this.cacheExpiry = Date.now() + this.CACHE_DURATION

    return exchangeInfos
  }

  private getExchangeName(id: string): string {
    const names: Record<string, string> = {
      'binance': 'Binance',
      'coinbase': 'Coinbase Pro',
      'kraken': 'Kraken',
      'bitfinex': 'Bitfinex',
      'huobi': 'Huobi Global',
      'okx': 'OKX',
      'kucoin': 'KuCoin',
      'bybit': 'Bybit',
      'gate': 'Gate.io',
      'mexc': 'MEXC',
      'bitget': 'Bitget',
      'gemini': 'Gemini',
      'bitstamp': 'Bitstamp'
    }
    return names[id] || id.charAt(0).toUpperCase() + id.slice(1)
  }

  async getExchangeTickers(exchangeId: string): Promise<TickerData[]> {
    const exchange = this.exchanges.get(exchangeId)
    if (!exchange) {
      throw new Error(`Exchange ${exchangeId} not found`)
    }

    try {
      console.log(`Fetching tickers for ${exchangeId}...`)

      // Check if this is a real CCXT exchange or fallback
      if (this.ccxt && exchange.loadMarkets) {
        // Real CCXT exchange
        await exchange.loadMarkets()

        let tickers: any = {}

        if (exchange.has.fetchTickers) {
          console.log(`Using fetchTickers for ${exchangeId}`)
          tickers = await exchange.fetchTickers()
        } else if (exchange.has.fetchTicker) {
          console.log(`Using fetchTicker for ${exchangeId}`)
          // Get major trading pairs
          const majorSymbols = Object.keys(exchange.markets)
            .filter(symbol =>
              symbol.includes('/USDT') ||
              symbol.includes('/USD') ||
              symbol.includes('/BTC') ||
              symbol.includes('/ETH')
            )
            .slice(0, 20) // Limit to 20 pairs to avoid rate limits

          for (const symbol of majorSymbols) {
            try {
              const ticker = await exchange.fetchTicker(symbol)
              tickers[symbol] = ticker
              // Respect rate limits
              await new Promise(resolve => setTimeout(resolve, exchange.rateLimit || 1000))
            } catch (error) {
              console.warn(`Failed to fetch ticker for ${symbol} on ${exchangeId}:`, error)
            }
          }
        }

        const tickerData: TickerData[] = []

        for (const [symbol, ticker] of Object.entries(tickers)) {
          try {
            const tickerInfo = ticker as any
            const quoteVolume = tickerInfo.quoteVolume || 0

            // Convert to USDT volume (simplified conversion)
            let volumeUSDT = quoteVolume
            if (symbol.includes('/USDT')) {
              volumeUSDT = quoteVolume
            } else if (symbol.includes('/USD')) {
              volumeUSDT = quoteVolume
            } else if (symbol.includes('/BTC')) {
              volumeUSDT = quoteVolume * 45000 // Approximate BTC price
            } else if (symbol.includes('/ETH')) {
              volumeUSDT = quoteVolume * 2500 // Approximate ETH price
            }

            const data: TickerData = {
              symbol: tickerInfo.symbol,
              timestamp: tickerInfo.timestamp || Date.now(),
              datetime: tickerInfo.datetime || new Date().toISOString(),
              high: tickerInfo.high || 0,
              low: tickerInfo.low || 0,
              bid: tickerInfo.bid || 0,
              bidVolume: tickerInfo.bidVolume || 0,
              ask: tickerInfo.ask || 0,
              askVolume: tickerInfo.askVolume || 0,
              vwap: tickerInfo.vwap || 0,
              open: tickerInfo.open || 0,
              close: tickerInfo.close || 0,
              last: tickerInfo.last || 0,
              previousClose: tickerInfo.previousClose || 0,
              change: tickerInfo.change || 0,
              percentage: tickerInfo.percentage || 0,
              average: tickerInfo.average || 0,
              baseVolume: tickerInfo.baseVolume || 0,
              quoteVolume: quoteVolume,
              info: tickerInfo.info || {},
              exchangeId: exchangeId,
              volumeUSDT: volumeUSDT
            }

            tickerData.push(data)
          } catch (error) {
            console.warn(`Failed to process ticker data for ${symbol}:`, error)
          }
        }

        console.log(`✓ Fetched ${tickerData.length} tickers for ${exchangeId}`)
        return tickerData.sort((a, b) => (b.volumeUSDT || 0) - (a.volumeUSDT || 0))
      } else {
        // Fallback to mock data
        console.log(`Using mock data for ${exchangeId}`)
        return this.generateMockTickers(exchangeId)
      }
    } catch (error) {
      console.error(`Failed to fetch tickers for ${exchangeId}:`, error)
      // Return mock data as fallback
      return this.generateMockTickers(exchangeId)
    }
  }

  private generateMockTickers(exchangeId: string): TickerData[] {
    const mockSymbols = [
      'BTC/USDT', 'ETH/USDT', 'BNB/USDT', 'ADA/USDT', 'SOL/USDT', 'XRP/USDT',
      'DOT/USDT', 'DOGE/USDT', 'AVAX/USDT', 'MATIC/USDT', 'LINK/USDT', 'UNI/USDT'
    ]

    return mockSymbols.map(symbol => {
      const basePrice = this.getBasePrice(symbol)
      const volume = Math.random() * 10000000 + 100000
      const change = (Math.random() - 0.5) * 20
      const last = basePrice * (1 + change / 100)

      return {
        symbol,
        timestamp: Date.now(),
        datetime: new Date().toISOString(),
        high: last * (1 + Math.random() * 0.05),
        low: last * (1 - Math.random() * 0.05),
        bid: last * 0.999,
        bidVolume: Math.random() * 1000,
        ask: last * 1.001,
        askVolume: Math.random() * 1000,
        vwap: last,
        open: basePrice,
        close: last,
        last,
        previousClose: basePrice,
        change: last - basePrice,
        percentage: change,
        average: (basePrice + last) / 2,
        baseVolume: volume / last,
        quoteVolume: volume,
        info: {},
        exchangeId,
        volumeUSDT: volume
      }
    }).sort((a, b) => (b.volumeUSDT || 0) - (a.volumeUSDT || 0))
  }

  private getBasePrice(symbol: string): number {
    const prices: Record<string, number> = {
      'BTC/USDT': 45000,
      'ETH/USDT': 2500,
      'BNB/USDT': 300,
      'ADA/USDT': 0.5,
      'SOL/USDT': 100,
      'XRP/USDT': 0.6,
      'DOT/USDT': 7,
      'DOGE/USDT': 0.08,
      'AVAX/USDT': 35,
      'MATIC/USDT': 0.9,
      'LINK/USDT': 15,
      'UNI/USDT': 6
    }
    return prices[symbol] || 1
  }

  async getExchangeVolume(exchangeId: string): Promise<ExchangeVolumeData> {
    try {
      const tickers = await this.getExchangeTickers(exchangeId)
      const totalVolumeUSDT = tickers.reduce((sum, ticker) => sum + (ticker.volumeUSDT || 0), 0)

      const exchange = this.exchanges.get(exchangeId)

      return {
        exchangeId,
        exchangeName: exchange?.name || this.getExchangeName(exchangeId),
        totalVolumeUSDT,
        tickerCount: tickers.length,
        status: 'ok',
        lastUpdated: Date.now()
      }
    } catch (error) {
      console.error(`Failed to get volume for ${exchangeId}:`, error)
      return {
        exchangeId,
        exchangeName: this.getExchangeName(exchangeId),
        totalVolumeUSDT: 0,
        tickerCount: 0,
        status: 'error',
        lastUpdated: Date.now()
      }
    }
  }

  async getAllExchangeVolumes(): Promise<ExchangeVolumeData[]> {
    const volumes: ExchangeVolumeData[] = []

    console.log('Fetching volumes for all exchanges...')
    for (const exchangeId of this.exchanges.keys()) {
      try {
        const volume = await this.getExchangeVolume(exchangeId)
        volumes.push(volume)
        console.log(`✓ Got volume for ${exchangeId}: ${volume.totalVolumeUSDT.toLocaleString()} USDT`)
        // Add delay to respect rate limits
        await new Promise(resolve => setTimeout(resolve, 2000))
      } catch (error) {
        console.warn(`✗ Failed to get volume for ${exchangeId}:`, error)
        volumes.push({
          exchangeId,
          exchangeName: this.getExchangeName(exchangeId),
          totalVolumeUSDT: 0,
          tickerCount: 0,
          status: 'error',
          lastUpdated: Date.now()
        })
      }
    }

    return volumes.sort((a, b) => b.totalVolumeUSDT - a.totalVolumeUSDT)
  }

  async getAllMarketTickers(): Promise<TickerData[]> {
    const allTickers: TickerData[] = []

    console.log('Fetching tickers for all exchanges...')
    for (const exchangeId of this.exchanges.keys()) {
      try {
        const tickers = await this.getExchangeTickers(exchangeId)
        allTickers.push(...tickers)
        console.log(`✓ Got ${tickers.length} tickers for ${exchangeId}`)
        // Add delay to respect rate limits
        await new Promise(resolve => setTimeout(resolve, 2000))
      } catch (error) {
        console.warn(`✗ Failed to get tickers for ${exchangeId}:`, error)
      }
    }

    console.log(`Total tickers collected: ${allTickers.length}`)
    return allTickers.sort((a, b) => (b.volumeUSDT || 0) - (a.volumeUSDT || 0))
  }
}

export const exchangeService = new ExchangeService()
export default ExchangeService
