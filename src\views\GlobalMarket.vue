<template>
  <div class="global-market">
    <div class="page-header">
      <h2>
        <el-icon><DataAnalysis /></el-icon>
        全市场交易分析
      </h2>
      <div class="header-actions">
        <el-button 
          type="primary" 
          :loading="loading" 
          @click="refreshData"
          :icon="Refresh"
        >
          刷新数据
        </el-button>
      </div>
    </div>

    <el-alert
      v-if="error"
      :title="error"
      type="error"
      show-icon
      closable
      @close="clearError"
      class="error-alert"
    />

    <!-- Market Summary Cards -->
    <div class="summary-cards">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-card class="summary-card">
            <div class="summary-content">
              <div class="summary-value">{{ marketSummary.totalExchanges }}</div>
              <div class="summary-label">活跃交易所</div>
            </div>
            <el-icon class="summary-icon"><Shop /></el-icon>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="summary-card">
            <div class="summary-content">
              <div class="summary-value">{{ marketSummary.totalPairs }}</div>
              <div class="summary-label">交易对总数</div>
            </div>
            <el-icon class="summary-icon"><DataLine /></el-icon>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="summary-card">
            <div class="summary-content">
              <div class="summary-value">{{ formatVolume(marketSummary.totalVolumeUSDT) }}</div>
              <div class="summary-label">总交易量 (USDT)</div>
            </div>
            <el-icon class="summary-icon"><Money /></el-icon>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="summary-card">
            <div class="summary-content">
              <div class="summary-value">{{ formatTime(marketSummary.lastUpdated) }}</div>
              <div class="summary-label">最后更新</div>
            </div>
            <el-icon class="summary-icon"><Clock /></el-icon>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- Charts Section -->
    <el-row :gutter="20" class="charts-section">
      <el-col :span="12">
        <el-card class="chart-card">
          <template #header>
            <span>交易所交易量分布</span>
          </template>
          <div class="chart-container">
            <v-chart 
              :option="exchangeVolumeChartOption" 
              style="height: 300px;"
              autoresize
            />
          </div>
        </el-card>
      </el-col>
      <el-col :span="12">
        <el-card class="chart-card">
          <template #header>
            <span>热门交易对分布</span>
          </template>
          <div class="chart-container">
            <v-chart 
              :option="topPairsChartOption" 
              style="height: 300px;"
              autoresize
            />
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- Top Exchanges Table -->
    <el-card class="table-card">
      <template #header>
        <div class="card-header">
          <span>交易所排行榜</span>
          <el-tag type="info">按24小时交易量排序</el-tag>
        </div>
      </template>
      
      <el-table
        :data="marketSummary.topExchanges"
        stripe
        style="width: 100%"
        max-height="400"
      >
        <el-table-column type="index" label="排名" width="80" />
        <el-table-column prop="exchangeName" label="交易所" min-width="150" />
        <el-table-column prop="totalVolumeUSDT" label="24h交易量 (USDT)" min-width="150" sortable>
          <template #default="{ row }">
            <span class="volume-value">{{ formatVolume(row.totalVolumeUSDT) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="tickerCount" label="交易对数量" width="120" sortable />
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag 
              :type="row.status === 'ok' ? 'success' : 'danger'"
              size="small"
            >
              {{ row.status === 'ok' ? '正常' : '异常' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="120">
          <template #default="{ row }">
            <el-button
              type="primary"
              size="small"
              @click="viewExchange(row.exchangeId)"
            >
              查看详情
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- Global Trading Pairs Table -->
    <el-card class="table-card">
      <template #header>
        <div class="card-header">
          <span>全市场热门交易对</span>
          <div class="header-controls">
            <el-input
              v-model="searchQuery"
              placeholder="搜索交易对..."
              :prefix-icon="Search"
              style="width: 200px; margin-right: 10px;"
              clearable
            />
            <el-tag type="info">按24小时交易量排序</el-tag>
          </div>
        </div>
      </template>

      <el-table
        :data="filteredGlobalTickers"
        v-loading="loading"
        stripe
        style="width: 100%"
        max-height="600"
      >
        <el-table-column type="index" label="排名" width="80" />
        <el-table-column prop="symbol" label="交易对" min-width="120" fixed="left" />
        <el-table-column prop="exchangeId" label="交易所" width="120" />
        <el-table-column prop="last" label="最新价格" width="120">
          <template #default="{ row }">
            <span class="price-value">{{ formatPrice(row.last) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="percentage" label="24h涨跌幅" width="120">
          <template #default="{ row }">
            <span 
              :class="['percentage-value', row.percentage >= 0 ? 'positive' : 'negative']"
            >
              {{ formatPercentage(row.percentage) }}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="volumeUSDT" label="24h交易量 (USDT)" min-width="150" sortable>
          <template #default="{ row }">
            <span class="volume-value">{{ formatVolume(row.volumeUSDT || 0) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="datetime" label="更新时间" width="150">
          <template #default="{ row }">
            {{ formatTime(row.timestamp) }}
          </template>
        </el-table-column>
      </el-table>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted, ref } from 'vue'
import { useRouter } from 'vue-router'
import { useExchangeStore } from '@/stores/exchangeStore'
import { DataAnalysis, Refresh, Shop, DataLine, Money, Clock, Search } from '@element-plus/icons-vue'
import VChart from 'vue-echarts'
import { use } from 'echarts/core'
import { CanvasRenderer } from 'echarts/renderers'
import { PieChart, BarChart } from 'echarts/charts'
import {
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent
} from 'echarts/components'
import dayjs from 'dayjs'

// Register ECharts components
use([
  CanvasRenderer,
  PieChart,
  BarChart,
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent
])

const router = useRouter()
const exchangeStore = useExchangeStore()

const searchQuery = ref('')

const {
  marketSummary,
  sortedAllTickersByVolume,
  loading,
  error,
  fetchExchanges,
  fetchExchangeVolumes,
  fetchAllMarketTickers,
  clearError
} = exchangeStore

const filteredGlobalTickers = computed(() => {
  if (!searchQuery.value) return sortedAllTickersByVolume.slice(0, 100) // Limit to top 100

  const query = searchQuery.value.toLowerCase()
  return sortedAllTickersByVolume
    .filter(ticker =>
      ticker.symbol.toLowerCase().includes(query) ||
      ticker.exchangeId.toLowerCase().includes(query)
    )
    .slice(0, 100)
})

// Chart options
const exchangeVolumeChartOption = computed(() => {
  const data = marketSummary.topExchanges.slice(0, 10).map(exchange => ({
    name: exchange.exchangeName,
    value: exchange.totalVolumeUSDT
  }))

  return {
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    legend: {
      orient: 'vertical',
      left: 'left'
    },
    series: [
      {
        name: '交易量',
        type: 'pie',
        radius: '50%',
        data: data,
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }
    ]
  }
})

const topPairsChartOption = computed(() => {
  const data = marketSummary.topPairs.slice(0, 10)

  return {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'value'
    },
    yAxis: {
      type: 'category',
      data: data.map(ticker => ticker.symbol)
    },
    series: [
      {
        name: '交易量 (USDT)',
        type: 'bar',
        data: data.map(ticker => ticker.volumeUSDT || 0),
        itemStyle: {
          color: '#3498db'
        }
      }
    ]
  }
})

function formatVolume(volume: number): string {
  if (volume >= 1e9) {
    return (volume / 1e9).toFixed(2) + 'B'
  } else if (volume >= 1e6) {
    return (volume / 1e6).toFixed(2) + 'M'
  } else if (volume >= 1e3) {
    return (volume / 1e3).toFixed(2) + 'K'
  }
  return volume.toFixed(2)
}

function formatPrice(price: number): string {
  if (price >= 1) {
    return price.toFixed(4)
  } else if (price >= 0.01) {
    return price.toFixed(6)
  } else {
    return price.toFixed(8)
  }
}

function formatPercentage(percentage: number): string {
  return (percentage >= 0 ? '+' : '') + percentage.toFixed(2) + '%'
}

function formatTime(timestamp: number): string {
  return dayjs(timestamp).format('HH:mm:ss')
}

function viewExchange(exchangeId: string) {
  router.push(`/exchange/${exchangeId}`)
}

async function refreshData() {
  await Promise.all([
    fetchExchanges(),
    fetchExchangeVolumes(),
    fetchAllMarketTickers()
  ])
}

onMounted(async () => {
  await refreshData()
})
</script>

<style scoped>
.global-market {
  max-width: 1200px;
  margin: 0 auto;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0;
  color: #2c3e50;
  display: flex;
  align-items: center;
  gap: 10px;
}

.error-alert {
  margin-bottom: 20px;
}

.summary-cards {
  margin-bottom: 20px;
}

.summary-card {
  position: relative;
  overflow: hidden;
}

.summary-content {
  position: relative;
  z-index: 2;
}

.summary-value {
  font-size: 24px;
  font-weight: bold;
  color: #2c3e50;
  margin-bottom: 5px;
}

.summary-label {
  font-size: 14px;
  color: #7f8c8d;
}

.summary-icon {
  position: absolute;
  right: 20px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 40px;
  color: #ecf0f1;
  z-index: 1;
}

.charts-section {
  margin-bottom: 20px;
}

.chart-card {
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.chart-container {
  padding: 10px;
}

.table-card {
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-controls {
  display: flex;
  align-items: center;
}

.volume-value {
  font-weight: 600;
  color: #27ae60;
}

.price-value {
  font-weight: 600;
  color: #2c3e50;
}

.percentage-value {
  font-weight: 600;
}

.percentage-value.positive {
  color: #27ae60;
}

.percentage-value.negative {
  color: #e74c3c;
}
</style>
