export interface ExchangeInfo {
  id: string
  name: string
  countries: string[]
  urls: {
    logo: string
    www: string
    api: string
  }
  has: {
    fetchTicker: boolean
    fetchTickers: boolean
    fetchOrderBook: boolean
    fetchTrades: boolean
    fetchOHLCV: boolean
  }
  rateLimit: number
  certified: boolean
  pro: boolean
  sandbox: boolean
  status: 'ok' | 'maintenance' | 'error'
  volume24h?: number
  volumeUSDT?: number
}

export interface TradingPair {
  symbol: string
  base: string
  quote: string
  active: boolean
  type: 'spot' | 'future' | 'option' | 'swap'
  spot: boolean
  margin: boolean
  future: boolean
  option: boolean
  contract: boolean
  linear: boolean
  inverse: boolean
  taker: number
  maker: number
  percentage: boolean
  tierBased: boolean
  contractSize?: number
  expiry?: number
  expiryDatetime?: string
  strike?: number
  optionType?: 'call' | 'put'
  precision: {
    amount: number
    price: number
  }
  limits: {
    amount: {
      min: number
      max: number
    }
    price: {
      min: number
      max: number
    }
    cost: {
      min: number
      max: number
    }
  }
  info: any
}

export interface TickerData {
  symbol: string
  timestamp: number
  datetime: string
  high: number
  low: number
  bid: number
  bidVolume: number
  ask: number
  askVolume: number
  vwap: number
  open: number
  close: number
  last: number
  previousClose: number
  change: number
  percentage: number
  average: number
  baseVolume: number
  quoteVolume: number
  info: any
  exchangeId: string
  volumeUSDT?: number
}

export interface ExchangeVolumeData {
  exchangeId: string
  exchangeName: string
  totalVolumeUSDT: number
  tickerCount: number
  status: 'ok' | 'error' | 'loading'
  lastUpdated: number
}

export interface MarketSummary {
  totalExchanges: number
  totalPairs: number
  totalVolumeUSDT: number
  topExchanges: ExchangeVolumeData[]
  topPairs: TickerData[]
  lastUpdated: number
}
