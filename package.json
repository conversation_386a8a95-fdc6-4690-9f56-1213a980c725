{"name": "crypto-exchange-monitor", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc && vite build", "preview": "vite preview", "type-check": "vue-tsc --noEmit"}, "dependencies": {"vue": "^3.4.0", "vue-router": "^4.2.5", "pinia": "^2.1.7", "ccxt": "^4.2.0", "element-plus": "^2.4.4", "@element-plus/icons-vue": "^2.3.1", "echarts": "^5.4.3", "vue-echarts": "^6.6.1", "axios": "^1.6.2", "dayjs": "^1.11.10"}, "devDependencies": {"@types/node": "^20.10.0", "@vitejs/plugin-vue": "^4.5.2", "typescript": "^5.3.0", "vite": "^5.0.0", "vue-tsc": "^1.8.25", "vite-plugin-node-polyfills": "^0.17.0"}}