<template>
  <div class="exchange-pairs">
    <div class="page-header">
      <div class="header-left">
        <el-button @click="goBack" :icon="ArrowLeft" circle />
        <div class="exchange-info">
          <h2>{{ exchangeInfo?.name || exchangeId }} 交易对</h2>
          <p class="exchange-subtitle">{{ exchangeId }} - 按24小时交易量排序</p>
        </div>
      </div>
      <div class="header-actions">
        <el-button 
          type="primary" 
          :loading="loading" 
          @click="refreshData"
          :icon="Refresh"
        >
          刷新数据
        </el-button>
      </div>
    </div>

    <el-alert
      v-if="error"
      :title="error"
      type="error"
      show-icon
      closable
      @close="clearError"
      class="error-alert"
    />

    <div class="stats-cards">
      <el-row :gutter="20">
        <el-col :span="8">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-value">{{ sortedTickersByVolume.length }}</div>
              <div class="stat-label">交易对数量</div>
            </div>
            <el-icon class="stat-icon"><DataLine /></el-icon>
          </el-card>
        </el-col>
        <el-col :span="8">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-value">{{ formatVolume(totalVolume) }}</div>
              <div class="stat-label">总交易量 (USDT)</div>
            </div>
            <el-icon class="stat-icon"><Money /></el-icon>
          </el-card>
        </el-col>
        <el-col :span="8">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-value">{{ lastUpdated }}</div>
              <div class="stat-label">最后更新</div>
            </div>
            <el-icon class="stat-icon"><Clock /></el-icon>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <el-card class="table-card">
      <template #header>
        <div class="card-header">
          <span>交易对列表</span>
          <div class="header-controls">
            <el-input
              v-model="searchQuery"
              placeholder="搜索交易对..."
              :prefix-icon="Search"
              style="width: 200px; margin-right: 10px;"
              clearable
            />
            <el-tag type="info">按24小时交易量排序</el-tag>
          </div>
        </div>
      </template>

      <el-table
        :data="filteredTickers"
        v-loading="loading"
        stripe
        style="width: 100%"
        :default-sort="{ prop: 'volumeUSDT', order: 'descending' }"
        max-height="600"
      >
        <el-table-column type="index" label="排名" width="80" />
        
        <el-table-column prop="symbol" label="交易对" min-width="120" fixed="left">
          <template #default="{ row }">
            <div class="symbol-cell">
              <span class="symbol-name">{{ row.symbol }}</span>
            </div>
          </template>
        </el-table-column>

        <el-table-column prop="last" label="最新价格" width="120" sortable>
          <template #default="{ row }">
            <span class="price-value">{{ formatPrice(row.last) }}</span>
          </template>
        </el-table-column>

        <el-table-column prop="percentage" label="24h涨跌幅" width="120" sortable>
          <template #default="{ row }">
            <span 
              :class="['percentage-value', row.percentage >= 0 ? 'positive' : 'negative']"
            >
              {{ formatPercentage(row.percentage) }}
            </span>
          </template>
        </el-table-column>

        <el-table-column prop="volumeUSDT" label="24h交易量 (USDT)" min-width="150" sortable>
          <template #default="{ row }">
            <div class="volume-cell">
              <div class="volume-value">{{ formatVolume(row.volumeUSDT || 0) }}</div>
              <div class="volume-bar">
                <div 
                  class="volume-progress" 
                  :style="{ width: getVolumePercentage(row.volumeUSDT || 0) + '%' }"
                ></div>
              </div>
            </div>
          </template>
        </el-table-column>

        <el-table-column prop="high" label="24h最高" width="120" sortable>
          <template #default="{ row }">
            <span class="price-value">{{ formatPrice(row.high) }}</span>
          </template>
        </el-table-column>

        <el-table-column prop="low" label="24h最低" width="120" sortable>
          <template #default="{ row }">
            <span class="price-value">{{ formatPrice(row.low) }}</span>
          </template>
        </el-table-column>

        <el-table-column prop="datetime" label="更新时间" width="150">
          <template #default="{ row }">
            {{ formatTime(row.timestamp) }}
          </template>
        </el-table-column>
      </el-table>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted, ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useExchangeStore } from '@/stores/exchangeStore'
import { ArrowLeft, Refresh, DataLine, Money, Clock, Search } from '@element-plus/icons-vue'
import dayjs from 'dayjs'

const route = useRoute()
const router = useRouter()
const exchangeStore = useExchangeStore()

const exchangeId = computed(() => route.params.id as string)
const searchQuery = ref('')

const {
  currentExchangeTickers,
  sortedTickersByVolume,
  loading,
  error,
  fetchExchangeTickers,
  getExchangeById,
  clearError
} = exchangeStore

const exchangeInfo = computed(() => getExchangeById(exchangeId.value))

const totalVolume = computed(() => {
  return currentExchangeTickers.reduce((sum, ticker) => sum + (ticker.volumeUSDT || 0), 0)
})

const lastUpdated = computed(() => {
  if (currentExchangeTickers.length === 0) return '--'
  const latest = Math.max(...currentExchangeTickers.map(ticker => ticker.timestamp))
  return dayjs(latest).format('HH:mm:ss')
})

const maxVolume = computed(() => {
  return Math.max(...currentExchangeTickers.map(ticker => ticker.volumeUSDT || 0))
})

const filteredTickers = computed(() => {
  if (!searchQuery.value) return sortedTickersByVolume

  const query = searchQuery.value.toLowerCase()
  return sortedTickersByVolume.filter(ticker =>
    ticker.symbol.toLowerCase().includes(query)
  )
})

function formatVolume(volume: number): string {
  if (volume >= 1e9) {
    return (volume / 1e9).toFixed(2) + 'B'
  } else if (volume >= 1e6) {
    return (volume / 1e6).toFixed(2) + 'M'
  } else if (volume >= 1e3) {
    return (volume / 1e3).toFixed(2) + 'K'
  }
  return volume.toFixed(2)
}

function formatPrice(price: number): string {
  if (price >= 1) {
    return price.toFixed(4)
  } else if (price >= 0.01) {
    return price.toFixed(6)
  } else {
    return price.toFixed(8)
  }
}

function formatPercentage(percentage: number): string {
  return (percentage >= 0 ? '+' : '') + percentage.toFixed(2) + '%'
}

function formatTime(timestamp: number): string {
  return dayjs(timestamp).format('HH:mm:ss')
}

function getVolumePercentage(volume: number): number {
  return maxVolume.value > 0 ? (volume / maxVolume.value) * 100 : 0
}

function goBack() {
  router.push('/exchanges')
}

async function refreshData() {
  await fetchExchangeTickers(exchangeId.value)
}

onMounted(async () => {
  await refreshData()
})
</script>

<style scoped>
.exchange-pairs {
  max-width: 1200px;
  margin: 0 auto;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 15px;
}

.exchange-info h2 {
  margin: 0;
  color: #2c3e50;
}

.exchange-subtitle {
  margin: 5px 0 0 0;
  color: #7f8c8d;
  font-size: 14px;
}

.error-alert {
  margin-bottom: 20px;
}

.stats-cards {
  margin-bottom: 20px;
}

.stat-card {
  position: relative;
  overflow: hidden;
}

.stat-content {
  position: relative;
  z-index: 2;
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  color: #2c3e50;
  margin-bottom: 5px;
}

.stat-label {
  font-size: 14px;
  color: #7f8c8d;
}

.stat-icon {
  position: absolute;
  right: 20px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 40px;
  color: #ecf0f1;
  z-index: 1;
}

.table-card {
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-controls {
  display: flex;
  align-items: center;
}

.symbol-cell {
  display: flex;
  align-items: center;
}

.symbol-name {
  font-weight: 600;
  color: #2c3e50;
}

.price-value {
  font-weight: 600;
  color: #2c3e50;
}

.percentage-value {
  font-weight: 600;
}

.percentage-value.positive {
  color: #27ae60;
}

.percentage-value.negative {
  color: #e74c3c;
}

.volume-cell {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.volume-value {
  font-weight: 600;
  color: #27ae60;
}

.volume-bar {
  width: 100%;
  height: 4px;
  background: #ecf0f1;
  border-radius: 2px;
  overflow: hidden;
}

.volume-progress {
  height: 100%;
  background: linear-gradient(90deg, #3498db, #2ecc71);
  transition: width 0.3s ease;
}
</style>
