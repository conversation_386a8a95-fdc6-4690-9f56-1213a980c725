# 加密货币交易所监控系统

一个基于 Vue 3 + TypeScript + Vite 的实时加密货币交易所数据监控系统，提供交易热度可视化展示。

## 功能特性

### 🏪 交易所监控
- **实时数据获取**: 支持多个主流加密货币交易所的实时数据获取
- **24小时交易量排序**: 按照 USDT 交易量对所有交易所进行排序
- **交易所状态监控**: 实时监控交易所的运行状态

### 📊 交易对分析
- **单个交易所交易对查看**: 查看指定交易所的所有交易对
- **交易对排序**: 按24小时交易量对交易对进行排序
- **价格变化监控**: 实时显示价格变化和涨跌幅

### 🌍 全市场分析
- **全市场交易对排序**: 对所有交易所的所有交易对按交易量排序
- **市场概览**: 显示总交易所数量、交易对数量、总交易量等统计信息
- **热门交易对**: 展示全市场最活跃的交易对

### 📈 数据可视化
- **交易量分布图**: 使用饼图展示各交易所的交易量分布
- **热门交易对柱状图**: 展示最活跃交易对的交易量对比
- **实时数据更新**: 支持手动刷新和自动更新

## 技术栈

- **前端框架**: Vue 3 (Composition API)
- **开发语言**: TypeScript
- **构建工具**: Vite
- **UI 组件库**: Element Plus
- **状态管理**: Pinia
- **路由管理**: Vue Router
- **图表库**: ECharts + Vue-ECharts
- **样式**: CSS3 + Flexbox
- **日期处理**: Day.js

## 项目结构

```
src/
├── components/          # 可复用组件
├── views/              # 页面组件
│   ├── ExchangeList.vue    # 交易所列表页
│   ├── ExchangePairs.vue   # 交易对详情页
│   └── GlobalMarket.vue    # 全市场分析页
├── stores/             # Pinia 状态管理
│   └── exchangeStore.ts    # 交易所数据状态
├── services/           # 业务服务
│   └── exchangeService.ts  # 交易所数据服务
├── types/              # TypeScript 类型定义
│   └── exchange.ts         # 交易所相关类型
├── utils/              # 工具函数
├── assets/             # 静态资源
├── App.vue             # 根组件
└── main.ts             # 应用入口
```

## 快速开始

### 环境要求
- Node.js >= 16
- npm >= 7

### 安装依赖
```bash
npm install
```

### 启动开发服务器
```bash
npm run dev
```

### 构建生产版本
```bash
npm run build
```

### 预览生产版本
```bash
npm run preview
```

## 页面功能

### 1. 交易所排行页面 (`/exchanges`)
- 显示所有支持的交易所列表
- 按24小时交易量排序
- 显示交易所状态、交易对数量等信息
- 点击可查看具体交易所的交易对详情

### 2. 交易对详情页面 (`/exchange/:id`)
- 显示指定交易所的所有交易对
- 按24小时交易量排序
- 显示价格、涨跌幅、交易量等信息
- 支持搜索功能

### 3. 全市场分析页面 (`/global`)
- 全市场统计数据概览
- 交易所交易量分布饼图
- 热门交易对柱状图
- 全市场交易对排行榜

## 数据说明

当前版本使用模拟数据进行演示，包含以下交易所：
- Binance
- Coinbase Pro
- Kraken
- Bitfinex
- Huobi Global
- OKX
- KuCoin
- Bybit
- Gate.io
- MEXC
- Bitget
- Crypto.com
- Gemini
- Bitstamp

支持的主要交易对：
- BTC/USDT, ETH/USDT, BNB/USDT
- ADA/USDT, SOL/USDT, XRP/USDT
- DOT/USDT, DOGE/USDT, AVAX/USDT
- 等主流加密货币交易对

## 特色功能

### 🔄 实时数据更新
- 支持手动刷新数据
- 模拟真实的API调用延迟
- 数据缓存机制

### 🎨 美观的UI设计
- 现代化的界面设计
- 响应式布局
- 直观的数据可视化

### 📱 移动端适配
- 响应式设计
- 移动端友好的交互

### ⚡ 高性能
- Vue 3 Composition API
- TypeScript 类型安全
- Vite 快速构建

## 开发说明

### 扩展真实API
要集成真实的交易所API，可以：

1. 安装 ccxt 库：`npm install ccxt`
2. 修改 `src/services/exchangeService.ts`
3. 替换模拟数据为真实API调用
4. 处理跨域和API限制问题

### 添加新功能
- 在 `src/types/` 中定义新的类型
- 在 `src/services/` 中添加新的服务
- 在 `src/stores/` 中管理新的状态
- 在 `src/views/` 中创建新的页面

## 许可证

MIT License

## 贡献

欢迎提交 Issue 和 Pull Request！
