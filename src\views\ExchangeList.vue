<template>
  <div class="exchange-list">
    <div class="page-header">
      <h2>
        <el-icon><Shop /></el-icon>
        交易所24小时交易量排行
      </h2>
      <div class="header-actions">
        <el-button 
          type="primary" 
          :loading="loading" 
          @click="refreshData"
          :icon="Refresh"
        >
          刷新数据
        </el-button>
      </div>
    </div>

    <el-alert
      v-if="error"
      :title="error"
      type="error"
      show-icon
      closable
      @close="clearError"
      class="error-alert"
    />

    <div class="stats-cards">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-value">{{ exchanges.length }}</div>
              <div class="stat-label">支持的交易所</div>
            </div>
            <el-icon class="stat-icon"><Shop /></el-icon>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-value">{{ formatVolume(totalVolume) }}</div>
              <div class="stat-label">总交易量 (USDT)</div>
            </div>
            <el-icon class="stat-icon"><Money /></el-icon>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-value">{{ activeExchanges }}</div>
              <div class="stat-label">活跃交易所</div>
            </div>
            <el-icon class="stat-icon"><CircleCheck /></el-icon>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-value">{{ lastUpdated }}</div>
              <div class="stat-label">最后更新</div>
            </div>
            <el-icon class="stat-icon"><Clock /></el-icon>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <el-card class="table-card">
      <template #header>
        <div class="card-header">
          <span>交易所列表</span>
          <el-tag type="info">按24小时交易量排序</el-tag>
        </div>
      </template>

      <el-table
        :data="sortedExchangesByVolume"
        v-loading="loading"
        stripe
        style="width: 100%"
        :default-sort="{ prop: 'totalVolumeUSDT', order: 'descending' }"
      >
        <el-table-column type="index" label="排名" width="80" />
        
        <el-table-column prop="exchangeName" label="交易所" min-width="150">
          <template #default="{ row }">
            <div class="exchange-info">
              <div class="exchange-name">{{ row.exchangeName }}</div>
              <div class="exchange-id">{{ row.exchangeId }}</div>
            </div>
          </template>
        </el-table-column>

        <el-table-column prop="totalVolumeUSDT" label="24h交易量 (USDT)" min-width="150" sortable>
          <template #default="{ row }">
            <div class="volume-cell">
              <div class="volume-value">{{ formatVolume(row.totalVolumeUSDT) }}</div>
              <div class="volume-bar">
                <div 
                  class="volume-progress" 
                  :style="{ width: getVolumePercentage(row.totalVolumeUSDT) + '%' }"
                ></div>
              </div>
            </div>
          </template>
        </el-table-column>

        <el-table-column prop="tickerCount" label="交易对数量" width="120" sortable />

        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag 
              :type="row.status === 'ok' ? 'success' : row.status === 'error' ? 'danger' : 'warning'"
              size="small"
            >
              {{ row.status === 'ok' ? '正常' : row.status === 'error' ? '错误' : '维护中' }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column prop="lastUpdated" label="更新时间" width="150">
          <template #default="{ row }">
            {{ formatTime(row.lastUpdated) }}
          </template>
        </el-table-column>

        <el-table-column label="操作" width="120" fixed="right">
          <template #default="{ row }">
            <el-button
              type="primary"
              size="small"
              @click="viewExchangePairs(row.exchangeId)"
            >
              查看交易对
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useExchangeStore } from '@/stores/exchangeStore'
import { Shop, Refresh, Money, CircleCheck, Clock } from '@element-plus/icons-vue'
import dayjs from 'dayjs'

const router = useRouter()
const exchangeStore = useExchangeStore()

const {
  exchanges,
  exchangeVolumes,
  sortedExchangesByVolume,
  loading,
  error,
  fetchExchanges,
  fetchExchangeVolumes,
  clearError
} = exchangeStore

const totalVolume = computed(() => {
  return exchangeVolumes.reduce((sum, ex) => sum + ex.totalVolumeUSDT, 0)
})

const activeExchanges = computed(() => {
  return exchangeVolumes.filter(ex => ex.status === 'ok').length
})

const lastUpdated = computed(() => {
  const latest = Math.max(...exchangeVolumes.map(ex => ex.lastUpdated))
  return latest ? dayjs(latest).format('HH:mm:ss') : '--'
})

const maxVolume = computed(() => {
  return Math.max(...exchangeVolumes.map(ex => ex.totalVolumeUSDT))
})

function formatVolume(volume: number): string {
  if (volume >= 1e9) {
    return (volume / 1e9).toFixed(2) + 'B'
  } else if (volume >= 1e6) {
    return (volume / 1e6).toFixed(2) + 'M'
  } else if (volume >= 1e3) {
    return (volume / 1e3).toFixed(2) + 'K'
  }
  return volume.toFixed(2)
}

function formatTime(timestamp: number): string {
  return dayjs(timestamp).format('HH:mm:ss')
}

function getVolumePercentage(volume: number): number {
  return maxVolume.value > 0 ? (volume / maxVolume.value) * 100 : 0
}

function viewExchangePairs(exchangeId: string) {
  router.push(`/exchange/${exchangeId}`)
}

async function refreshData() {
  await Promise.all([
    fetchExchanges(),
    fetchExchangeVolumes()
  ])
}

onMounted(async () => {
  await refreshData()
})
</script>

<style scoped>
.exchange-list {
  max-width: 1200px;
  margin: 0 auto;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0;
  color: #2c3e50;
  display: flex;
  align-items: center;
  gap: 10px;
}

.error-alert {
  margin-bottom: 20px;
}

.stats-cards {
  margin-bottom: 20px;
}

.stat-card {
  position: relative;
  overflow: hidden;
}

.stat-content {
  position: relative;
  z-index: 2;
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  color: #2c3e50;
  margin-bottom: 5px;
}

.stat-label {
  font-size: 14px;
  color: #7f8c8d;
}

.stat-icon {
  position: absolute;
  right: 20px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 40px;
  color: #ecf0f1;
  z-index: 1;
}

.table-card {
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.exchange-info {
  display: flex;
  flex-direction: column;
}

.exchange-name {
  font-weight: 600;
  color: #2c3e50;
}

.exchange-id {
  font-size: 12px;
  color: #7f8c8d;
}

.volume-cell {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.volume-value {
  font-weight: 600;
  color: #27ae60;
}

.volume-bar {
  width: 100%;
  height: 4px;
  background: #ecf0f1;
  border-radius: 2px;
  overflow: hidden;
}

.volume-progress {
  height: 100%;
  background: linear-gradient(90deg, #3498db, #2ecc71);
  transition: width 0.3s ease;
}
</style>
