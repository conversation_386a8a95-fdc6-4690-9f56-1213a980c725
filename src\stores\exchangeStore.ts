import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { exchangeService } from '@/services/exchangeService'
import type { ExchangeInfo, TickerData, ExchangeVolumeData, MarketSummary } from '@/types/exchange'

export const useExchangeStore = defineStore('exchange', () => {
  // State
  const exchanges = ref<ExchangeInfo[]>([])
  const exchangeVolumes = ref<ExchangeVolumeData[]>([])
  const currentExchangeTickers = ref<TickerData[]>([])
  const allMarketTickers = ref<TickerData[]>([])
  const loading = ref(false)
  const error = ref<string | null>(null)

  // Computed
  const marketSummary = computed<MarketSummary>(() => {
    const totalVolumeUSDT = exchangeVolumes.value.reduce((sum, ex) => sum + ex.totalVolumeUSDT, 0)
    const totalPairs = allMarketTickers.value.length
    
    return {
      totalExchanges: exchanges.value.length,
      totalPairs,
      totalVolumeUSDT,
      topExchanges: exchangeVolumes.value.slice(0, 10),
      topPairs: allMarketTickers.value.slice(0, 20),
      lastUpdated: Date.now()
    }
  })

  const sortedExchangesByVolume = computed(() => {
    return [...exchangeVolumes.value].sort((a, b) => b.totalVolumeUSDT - a.totalVolumeUSDT)
  })

  const sortedTickersByVolume = computed(() => {
    return [...currentExchangeTickers.value].sort((a, b) => (b.volumeUSDT || 0) - (a.volumeUSDT || 0))
  })

  const sortedAllTickersByVolume = computed(() => {
    return [...allMarketTickers.value].sort((a, b) => (b.volumeUSDT || 0) - (a.volumeUSDT || 0))
  })

  // Actions
  async function fetchExchanges() {
    try {
      loading.value = true
      error.value = null
      exchanges.value = await exchangeService.getAllExchanges()
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to fetch exchanges'
      console.error('Error fetching exchanges:', err)
    } finally {
      loading.value = false
    }
  }

  async function fetchExchangeVolumes() {
    try {
      loading.value = true
      error.value = null
      exchangeVolumes.value = await exchangeService.getAllExchangeVolumes()
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to fetch exchange volumes'
      console.error('Error fetching exchange volumes:', err)
    } finally {
      loading.value = false
    }
  }

  async function fetchExchangeTickers(exchangeId: string) {
    try {
      loading.value = true
      error.value = null
      currentExchangeTickers.value = await exchangeService.getExchangeTickers(exchangeId)
    } catch (err) {
      error.value = err instanceof Error ? err.message : `Failed to fetch tickers for ${exchangeId}`
      console.error(`Error fetching tickers for ${exchangeId}:`, err)
    } finally {
      loading.value = false
    }
  }

  async function fetchAllMarketTickers() {
    try {
      loading.value = true
      error.value = null
      allMarketTickers.value = await exchangeService.getAllMarketTickers()
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to fetch all market tickers'
      console.error('Error fetching all market tickers:', err)
    } finally {
      loading.value = false
    }
  }

  async function refreshExchangeData(exchangeId: string) {
    try {
      loading.value = true
      error.value = null
      
      // Fetch both exchange volume and tickers
      const [volume, tickers] = await Promise.all([
        exchangeService.getExchangeVolume(exchangeId),
        exchangeService.getExchangeTickers(exchangeId)
      ])
      
      // Update exchange volume in the list
      const index = exchangeVolumes.value.findIndex(ex => ex.exchangeId === exchangeId)
      if (index !== -1) {
        exchangeVolumes.value[index] = volume
      } else {
        exchangeVolumes.value.push(volume)
      }
      
      currentExchangeTickers.value = tickers
    } catch (err) {
      error.value = err instanceof Error ? err.message : `Failed to refresh data for ${exchangeId}`
      console.error(`Error refreshing data for ${exchangeId}:`, err)
    } finally {
      loading.value = false
    }
  }

  function clearError() {
    error.value = null
  }

  function getExchangeById(id: string): ExchangeInfo | undefined {
    return exchanges.value.find(ex => ex.id === id)
  }

  function getExchangeVolumeById(id: string): ExchangeVolumeData | undefined {
    return exchangeVolumes.value.find(ex => ex.exchangeId === id)
  }

  return {
    // State
    exchanges,
    exchangeVolumes,
    currentExchangeTickers,
    allMarketTickers,
    loading,
    error,
    
    // Computed
    marketSummary,
    sortedExchangesByVolume,
    sortedTickersByVolume,
    sortedAllTickersByVolume,
    
    // Actions
    fetchExchanges,
    fetchExchangeVolumes,
    fetchExchangeTickers,
    fetchAllMarketTickers,
    refreshExchangeData,
    clearError,
    getExchangeById,
    getExchangeVolumeById
  }
})
